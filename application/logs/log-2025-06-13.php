<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-13 14:50:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-13'
AND   (
('09:29' BETWEEN start_time AND end_time)
OR ('11:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:29' AND '11:35')
OR (end_time BETWEEN '09:29' AND '11:35')
 )
ERROR - 2025-06-13 16:07:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-13'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('12:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '12:33')
OR (end_time BETWEEN '09:36' AND '12:33')
 )
