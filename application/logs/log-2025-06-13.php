<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-13 14:50:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-13'
AND   (
('09:29' BETWEEN start_time AND end_time)
OR ('11:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:29' AND '11:35')
OR (end_time BETWEEN '09:29' AND '11:35')
 )
ERROR - 2025-06-13 16:07:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-13'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('12:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '12:33')
OR (end_time BETWEEN '09:36' AND '12:33')
 )
ERROR - 2025-06-13 16:31:06 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.`user_id`, `u`.`name` as `employee_name`
FROM `task_assign` `ta`
JOIN `tasks...' at line 1 - Invalid query: SELECT `DISTINCT` `ta`.`user_id`, `u`.`name` as `employee_name`
FROM `task_assign` `ta`
JOIN `tasks` `t` ON `t`.`id` = `ta`.`task_id`
JOIN `users` `u` ON `u`.`id` = `ta`.`user_id`
WHERE `t`.`project_id` = '4'
AND `ta`.`job_date` >= '2025-06-01'
AND `ta`.`job_date` <= '2025-06-13'
AND `u`.`employee_status` = 1
AND `ta`.`time_taken` IS NOT NULL
AND `ta`.`time_taken` != '00:00:00'
ORDER BY `u`.`name` ASC
ERROR - 2025-06-13 16:32:40 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 143
ERROR - 2025-06-13 16:32:58 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 145
ERROR - 2025-06-13 16:32:59 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 145
ERROR - 2025-06-13 16:33:12 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:35 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 16:33:35 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:36 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:38 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:38 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:39 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:34:29 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:37:59 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 16:57:46 --> <p>You did not select a file to upload.</p>
