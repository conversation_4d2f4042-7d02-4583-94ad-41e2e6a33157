<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-13 14:50:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-13'
AND   (
('09:29' BETWEEN start_time AND end_time)
OR ('11:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:29' AND '11:35')
OR (end_time BETWEEN '09:29' AND '11:35')
 )
ERROR - 2025-06-13 16:07:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-13'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('12:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '12:33')
OR (end_time BETWEEN '09:36' AND '12:33')
 )
ERROR - 2025-06-13 16:31:06 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.`user_id`, `u`.`name` as `employee_name`
FROM `task_assign` `ta`
JOIN `tasks...' at line 1 - Invalid query: SELECT `DISTINCT` `ta`.`user_id`, `u`.`name` as `employee_name`
FROM `task_assign` `ta`
JOIN `tasks` `t` ON `t`.`id` = `ta`.`task_id`
JOIN `users` `u` ON `u`.`id` = `ta`.`user_id`
WHERE `t`.`project_id` = '4'
AND `ta`.`job_date` >= '2025-06-01'
AND `ta`.`job_date` <= '2025-06-13'
AND `u`.`employee_status` = 1
AND `ta`.`time_taken` IS NOT NULL
AND `ta`.`time_taken` != '00:00:00'
ORDER BY `u`.`name` ASC
ERROR - 2025-06-13 16:32:40 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 143
ERROR - 2025-06-13 16:32:58 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 145
ERROR - 2025-06-13 16:32:59 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 145
ERROR - 2025-06-13 16:33:12 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:35 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 16:33:35 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:36 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:38 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:38 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:33:39 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:34:29 --> Severity: error --> Exception: Call to undefined method CI_Loader::calculate_column_total() /home/<USER>/ci_apps/application/views/app/project_report/project_employee.php 148
ERROR - 2025-06-13 16:37:59 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 16:57:46 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:00:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-13'
AND   (
('14:22' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:22' AND '17:40')
OR (end_time BETWEEN '14:22' AND '17:40')
 )
ERROR - 2025-06-13 17:15:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-13'
AND   (
('09:25' BETWEEN start_time AND end_time)
OR ('13:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:25' AND '13:25')
OR (end_time BETWEEN '09:25' AND '13:25')
 )
ERROR - 2025-06-13 17:28:08 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:28:56 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:30:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-13'
AND   (
('14:29' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:29' AND '17:30')
OR (end_time BETWEEN '14:29' AND '17:30')
 )
ERROR - 2025-06-13 17:32:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-13'
AND   (
('14:04' BETWEEN start_time AND end_time)
OR ('18:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:04' AND '18:07')
OR (end_time BETWEEN '14:04' AND '18:07')
 )
ERROR - 2025-06-13 17:32:35 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:32:56 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:33:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-13'
AND   (
('14:05' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:05' AND '17:30')
OR (end_time BETWEEN '14:05' AND '17:30')
 )
ERROR - 2025-06-13 17:36:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-13'
AND   (
('14:15' BETWEEN start_time AND end_time)
OR ('17:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:15' AND '17:35')
OR (end_time BETWEEN '14:15' AND '17:35')
 )
ERROR - 2025-06-13 17:40:32 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:41:53 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 17:44:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-13'
AND   (
('09:28' BETWEEN start_time AND end_time)
OR ('11:23' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:28' AND '11:23')
OR (end_time BETWEEN '09:28' AND '11:23')
 )
ERROR - 2025-06-13 17:46:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-13'
AND   (
('11:48' BETWEEN start_time AND end_time)
OR ('13:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:48' AND '13:25')
OR (end_time BETWEEN '11:48' AND '13:25')
 )
ERROR - 2025-06-13 17:54:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-13'
AND   (
('09:24' BETWEEN start_time AND end_time)
OR ('12:29' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:24' AND '12:29')
OR (end_time BETWEEN '09:24' AND '12:29')
 )
ERROR - 2025-06-13 17:55:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-13'
AND   (
('14:14' BETWEEN start_time AND end_time)
OR ('17:53' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:14' AND '17:53')
OR (end_time BETWEEN '14:14' AND '17:53')
 )
ERROR - 2025-06-13 17:56:11 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 18:00:03 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-13'
AND   (
('09:15' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:15' AND '11:10')
OR (end_time BETWEEN '09:15' AND '11:10')
 )
ERROR - 2025-06-13 18:00:54 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-13'
AND   (
('11:50' BETWEEN start_time AND end_time)
OR ('12:36' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:50' AND '12:36')
OR (end_time BETWEEN '11:50' AND '12:36')
 )
ERROR - 2025-06-13 18:02:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-13'
AND   (
('09:40' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:40' AND '11:00')
OR (end_time BETWEEN '09:40' AND '11:00')
 )
ERROR - 2025-06-13 18:02:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-13'
AND   (
('13:45' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:45' AND '17:00')
OR (end_time BETWEEN '13:45' AND '17:00')
 )
ERROR - 2025-06-13 18:02:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-13'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '13:30')
OR (end_time BETWEEN '11:20' AND '13:30')
 )
ERROR - 2025-06-13 18:03:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-13'
AND   (
('12:30' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:30' AND '13:30')
OR (end_time BETWEEN '12:30' AND '13:30')
 )
ERROR - 2025-06-13 18:03:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-13'
AND   (
('14:25' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:25' AND '15:30')
OR (end_time BETWEEN '14:25' AND '15:30')
 )
ERROR - 2025-06-13 18:03:35 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 18:04:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-13'
AND   (
('15:31' BETWEEN start_time AND end_time)
OR ('16:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:31' AND '16:45')
OR (end_time BETWEEN '15:31' AND '16:45')
 )
ERROR - 2025-06-13 18:05:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-13'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('17:32' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '17:32')
OR (end_time BETWEEN '17:01' AND '17:32')
 )
ERROR - 2025-06-13 18:05:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-13'
AND   (
('09:44' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:44' AND '11:10')
OR (end_time BETWEEN '09:44' AND '11:10')
 )
ERROR - 2025-06-13 18:06:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-13'
AND   (
('17:35' BETWEEN start_time AND end_time)
OR ('18:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:35' AND '18:05')
OR (end_time BETWEEN '17:35' AND '18:05')
 )
ERROR - 2025-06-13 18:06:34 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-13'
AND   (
('11:37' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:37' AND '12:30')
OR (end_time BETWEEN '11:37' AND '12:30')
 )
ERROR - 2025-06-13 18:06:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-13'
AND   (
('14:16' BETWEEN start_time AND end_time)
OR ('15:19' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:16' AND '15:19')
OR (end_time BETWEEN '14:16' AND '15:19')
 )
ERROR - 2025-06-13 18:08:16 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 18:09:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-13'
AND   (
('13:30' BETWEEN start_time AND end_time)
OR ('15:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:30' AND '15:00')
OR (end_time BETWEEN '13:30' AND '15:00')
 )
ERROR - 2025-06-13 18:10:03 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-13 18:10:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-13'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '15:30')
OR (end_time BETWEEN '15:01' AND '15:30')
 )
ERROR - 2025-06-13 18:12:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-13'
AND   (
('15:20' BETWEEN start_time AND end_time)
OR ('18:12' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:20' AND '18:12')
OR (end_time BETWEEN '15:20' AND '18:12')
 )
ERROR - 2025-06-13 18:12:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-13'
AND   (
('16:46' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:46' AND '18:20')
OR (end_time BETWEEN '16:46' AND '18:20')
 )
