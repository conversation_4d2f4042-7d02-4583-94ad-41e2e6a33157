<?php
$project_id = $this->input->get('project_id') ?? '';
$start_date = $this->input->get('start_date') ?? '';
$end_date = $this->input->get('end_date') ?? '';
?>

<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        
        <div class="card-body p-1">
            <form method="get" action="">
                <div class="shadow_pro row p-1" style="max-width: 1200px;">
                    <div class="col-12 col-lg-4 p-2">
                        <select name="project_id" id="project_id" class="form-control select2" required>
                            <option value="">Choose Project</option>
                            <?php
                            if (isset($projects)){
                                foreach ($projects as $id => $title){
                                    $selected = $id == $project_id ? 'selected' : '';
                                    echo "<option value='{$id}' {$selected}>{$title}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="start_date" id="start_date" value="<?= $start_date ?>"
                               class="form-control" placeholder="Start Date (Optional)">
                        <small class="text-muted">Leave empty for last 30 days</small>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="end_date" id="end_date" value="<?= $end_date ?>"
                               class="form-control" placeholder="End Date (Optional)">
                        <small class="text-muted">Leave empty for today</small>
                    </div>
                    <div class="col-6 col-lg-2 p-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar"></i> Generate Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if (!empty($error_message)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Error</h5>
                <p><?= htmlspecialchars($error_message) ?></p>
            </div>
        </div>
    </div>
</div>
<?php elseif (!empty($report_data)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-2 mb-3">
            <h4>Project Employee Work Report</h4>
            <?php if (!empty($projects[$project_id])): ?>
                <h5 class="text-muted">Project: <?= $projects[$project_id] ?></h5>
            <?php endif; ?>
            <p class="text-muted">
                Period: <?= date('d-m-Y', strtotime($actual_start_date ?? $start_date)) ?> to <?= date('d-m-Y', strtotime($actual_end_date ?? $end_date)) ?>
                <?php if (empty($start_date) || empty($end_date)): ?>
                    <br><small class="text-info">(Default: Last 30 days)</small>
                <?php endif; ?>
            </p>
        </div>
        
        <!-- Summary Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3 class="card-title">Total Project Hours</h3>
                        <h2 class="display-4"><?= $total_hours ?></h2>
                        <p class="card-text">Combined work hours of all employees</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Report Table -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="thead-dark">
                    <tr>
                        <th style="position: sticky; left: 0; background-color: #343a40; z-index: 10;">Employee Name</th>
                        <th class="text-center">Total Hours</th>
                        <th class="text-center">Last 7 Days</th>
                        <th class="text-center">Last 15 Days</th>
                        <th class="text-center">Last 30 Days</th>
                        <th class="text-center">Last 90 Days</th>
                        <th class="text-center">Last 180 Days</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($report_data as $employee): ?>
                    <tr>
                        <td style="position: sticky; left: 0; background-color: #f8f9fa; font-weight: bold; z-index: 5;">
                            <?= htmlspecialchars($employee['employee_name']) ?>
                        </td>
                        <td class="text-center font-weight-bold text-primary">
                            <?= $employee['total_hours'] ?>
                        </td>
                        <td class="text-center">
                            <?= $employee['last_7_days'] ?>
                        </td>
                        <td class="text-center">
                            <?= $employee['last_15_days'] ?>
                        </td>
                        <td class="text-center">
                            <?= $employee['last_30_days'] ?>
                        </td>
                        <td class="text-center">
                            <?= $employee['last_90_days'] ?>
                        </td>
                        <td class="text-center">
                            <?= $employee['last_180_days'] ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="thead-light">
                    <tr>
                        <th style="position: sticky; left: 0; background-color: #e9ecef; z-index: 10;">
                            <strong>TOTAL</strong>
                        </th>
                        <th class="text-center text-primary">
                            <strong><?= $total_hours ?></strong>
                        </th>
                        <th class="text-center">
                            <strong><?= $column_totals['last_7_days'] ?? '00:00:00' ?></strong>
                        </th>
                        <th class="text-center">
                            <strong><?= $column_totals['last_15_days'] ?? '00:00:00' ?></strong>
                        </th>
                        <th class="text-center">
                            <strong><?= $column_totals['last_30_days'] ?? '00:00:00' ?></strong>
                        </th>
                        <th class="text-center">
                            <strong><?= $column_totals['last_90_days'] ?? '00:00:00' ?></strong>
                        </th>
                        <th class="text-center">
                            <strong><?= $column_totals['last_180_days'] ?? '00:00:00' ?></strong>
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <?php if (empty($report_data)): ?>
        <div class="text-center p-4">
            <div class="alert alert-info">
                <h5>No Data Found</h5>
                <p>No employees worked on this project during the selected date range.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php else: ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-warning">
                <h5><i class="fas fa-info-circle"></i> Generate Report</h5>
                <p>Please select a project to generate the employee work report.</p>
                <p><small class="text-muted">Date range is optional - if not specified, the report will show data for the last 30 days.</small></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
    .table-responsive {
        overflow-x: auto;
    }
    
    .table th, .table td {
        white-space: nowrap;
        vertical-align: middle;
    }
    
    .table thead th {
        border-bottom: 2px solid #dee2e6;
    }
    
    .table tfoot th {
        border-top: 2px solid #dee2e6;
    }
    
    .shadow-pro {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.375rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
        font-weight: 300;
        line-height: 1.2;
    }
</style>

<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Choose Project",
        allowClear: true
    });
});
</script>
