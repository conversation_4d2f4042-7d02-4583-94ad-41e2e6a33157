<?php
$project_id = $this->input->get('project_id') ?? '';
$start_date = $this->input->get('start_date') ?? '';
$end_date = $this->input->get('end_date') ?? '';
?>

<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        
        <div class="card-body p-1">
            <form method="get" action="">
                <div class="shadow_pro row p-1" style="max-width: 1200px;">
                    <div class="col-12 col-lg-4 p-2">
                        <select name="project_id" id="project_id" class="form-control select2" required>
                            <option value="">Choose Project</option>
                            <?php
                            if (isset($projects)){
                                foreach ($projects as $id => $title){
                                    $selected = $id == $project_id ? 'selected' : '';
                                    echo "<option value='{$id}' {$selected}>{$title}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="start_date" id="start_date" value="<?= $start_date ?>"
                               class="form-control" placeholder="Start Date (Optional)">
                        <small class="text-muted">Leave empty for last 30 days</small>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="end_date" id="end_date" value="<?= $end_date ?>"
                               class="form-control" placeholder="End Date (Optional)">
                        <small class="text-muted">Leave empty for today</small>
                    </div>
                    <div class="col-6 col-lg-2 p-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar"></i> Generate Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if (!empty($error_message)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Error</h5>
                <p><?= htmlspecialchars($error_message) ?></p>
            </div>
        </div>
    </div>
</div>
<?php elseif (!empty($report_data)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="report-header mb-4">
            <div class="row">
                <div class="col-md-8">
                    <h3 class="report-title">
                        <i class="fas fa-users text-primary"></i>
                        Project Employee Work Report
                    </h3>
                    <?php if (!empty($projects[$project_id])): ?>
                        <h5 class="project-name">
                            <i class="fas fa-project-diagram text-secondary"></i>
                            <?= htmlspecialchars($projects[$project_id]) ?>
                        </h5>
                    <?php endif; ?>
                </div>
                <div class="col-md-4 text-right">
                    <div class="report-period">
                        <small class="text-muted d-block">Report Period</small>
                        <strong class="text-dark">
                            <?= date('d M Y', strtotime($actual_start_date ?? $start_date)) ?>
                            <span class="text-muted">to</span>
                            <?= date('d M Y', strtotime($actual_end_date ?? $end_date)) ?>
                        </strong>
                        <?php if (empty($start_date) || empty($end_date)): ?>
                            <br><small class="badge badge-info">Default: Last 30 days</small>
                        <?php endif; ?>
                        <?php if (!empty($report_data)): ?>
                            <div class="mt-2">
                                <small class="text-muted d-block">Employees</small>
                                <span class="badge badge-light"><?= count($report_data) ?> Total</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Legend -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="legend-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-info"></i> Employee Status</h6>
                            <span class="badge badge-success mr-2">Active</span> Current employees
                            <span class="badge badge-secondary ml-3">Inactive</span> Former employees with work records
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-palette text-primary"></i> Data Highlights</h6>
                            <span class="legend-item primary">Total Date Range</span>
                            <span class="legend-item total">Total Worked (All Time)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Table -->
        <div class="table-responsive professional-table">
            <table class="table table-hover">
                <thead class="table-header">
                    <tr>
                        <th class="employee-name-col">
                            <i class="fas fa-user text-primary"></i>
                            Employee Name
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-calendar-alt text-info"></i>
                            <div class="header-text">Total Date Range</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-success"></i>
                            <div class="header-text">Last 7 Days</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-success"></i>
                            <div class="header-text">Last 15 Days</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-success"></i>
                            <div class="header-text">Last 30 Days</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-warning"></i>
                            <div class="header-text">Last 60 Days</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-warning"></i>
                            <div class="header-text">Last 90 Days</div>
                        </th>
                        <th class="time-col text-center">
                            <i class="fas fa-clock text-warning"></i>
                            <div class="header-text">Last 180 Days</div>
                        </th>
                        <th class="time-col text-center total-worked-col">
                            <i class="fas fa-chart-bar text-danger"></i>
                            <div class="header-text">Total Worked</div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($report_data as $index => $employee): ?>
                    <tr class="data-row <?= $employee['employee_status'] == 0 ? 'inactive-employee' : '' ?>">
                        <td class="employee-name-cell">
                            <div class="employee-info">
                                <span class="employee-name">
                                    <?= htmlspecialchars($employee['employee_name']) ?>
                                </span>
                                <?php if ($employee['employee_status'] == 0): ?>
                                    <span class="badge badge-secondary badge-sm ml-2">Inactive</span>
                                <?php else: ?>
                                    <span class="badge badge-success badge-sm ml-2">Active</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="time-cell text-center primary-data">
                            <span class="time-value"><?= $employee['total_date_range'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_7_days'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_15_days'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_30_days'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_60_days'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_90_days'] ?></span>
                        </td>
                        <td class="time-cell text-center">
                            <span class="time-value"><?= $employee['last_180_days'] ?></span>
                        </td>
                        <td class="time-cell text-center total-worked-cell">
                            <span class="time-value total-value"><?= $employee['total_worked'] ?></span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-footer">
                    <tr class="totals-row">
                        <th class="employee-name-cell totals-label">
                            <i class="fas fa-calculator text-primary"></i>
                            <strong>TOTALS</strong>
                        </th>
                        <th class="time-cell text-center primary-total">
                            <div class="total-value">
                                <strong><?= $column_totals['total_date_range'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_7_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_15_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_30_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_60_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_90_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center">
                            <div class="total-value">
                                <strong><?= $column_totals['last_180_days'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                        <th class="time-cell text-center grand-total">
                            <div class="total-value">
                                <strong><?= $column_totals['total_worked'] ?? '0 hrs 0 min' ?></strong>
                            </div>
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <?php if (empty($report_data)): ?>
        <div class="text-center p-4">
            <div class="alert alert-info">
                <h5>No Data Found</h5>
                <p>No employees worked on this project during the selected date range.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php else: ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-warning">
                <h5><i class="fas fa-info-circle"></i> Generate Report</h5>
                <p>Please select a project to generate the employee work report.</p>
                <p><small class="text-muted">Date range is optional - if not specified, the report will show data for the last 30 days.</small></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
    /* Professional Report Styling */
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .report-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: white;
    }

    .project-name {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 0;
        color: rgba(255,255,255,0.9);
    }

    .report-period {
        background: rgba(255,255,255,0.15);
        padding: 15px;
        border-radius: 8px;
        backdrop-filter: blur(10px);
    }

    /* Professional Table Design */
    .professional-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid #e3e6f0;
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    /* Table Header */
    .table-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
    }

    .table-header th {
        border: none;
        padding: 18px 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.8rem;
        position: relative;
    }

    .employee-name-col {
        position: sticky;
        left: 0;
        z-index: 10;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
        min-width: 200px;
        border-right: 2px solid rgba(255,255,255,0.2) !important;
    }

    .time-col {
        min-width: 120px;
    }

    .total-worked-col {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
        border-left: 2px solid rgba(255,255,255,0.2) !important;
    }

    .header-text {
        margin-top: 5px;
        font-size: 0.75rem;
    }

    /* Table Body */
    .data-row {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f3f4;
    }

    .data-row:hover {
        background-color: #f8f9ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }

    .inactive-employee {
        background-color: #fafafa;
        opacity: 0.8;
    }

    .inactive-employee:hover {
        background-color: #f0f0f0;
    }

    .employee-name-cell {
        position: sticky;
        left: 0;
        z-index: 5;
        background: white;
        border-right: 2px solid #e3e6f0;
        padding: 15px 12px;
        min-width: 200px;
    }

    .inactive-employee .employee-name-cell {
        background: #fafafa;
    }

    .employee-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .employee-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
    }

    .badge-sm {
        font-size: 0.7rem;
        padding: 3px 8px;
    }

    .time-cell {
        padding: 15px 8px;
        vertical-align: middle;
        border-right: 1px solid #f1f3f4;
    }

    .time-value {
        display: inline-block;
        padding: 6px 12px;
        background: #f8f9fa;
        border-radius: 20px;
        font-weight: 500;
        color: #495057;
        font-size: 0.85rem;
        min-width: 80px;
        transition: all 0.2s ease;
    }

    .primary-data .time-value {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        font-weight: 600;
    }

    .total-worked-cell .time-value {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        font-weight: 600;
    }

    .data-row:hover .time-value {
        transform: scale(1.05);
    }

    /* Table Footer */
    .table-footer {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
    }

    .totals-row th {
        border: none;
        padding: 20px 12px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .totals-label {
        position: sticky;
        left: 0;
        z-index: 10;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
        border-right: 2px solid rgba(255,255,255,0.2) !important;
    }

    .total-value {
        background: rgba(255,255,255,0.15);
        padding: 8px 12px;
        border-radius: 20px;
        backdrop-filter: blur(5px);
        display: inline-block;
        min-width: 90px;
    }

    .primary-total .total-value {
        background: rgba(52, 152, 219, 0.3);
        border: 1px solid rgba(52, 152, 219, 0.5);
    }

    .grand-total .total-value {
        background: rgba(231, 76, 60, 0.3);
        border: 1px solid rgba(231, 76, 60, 0.5);
    }

    /* Responsive Design */
    .table-responsive {
        overflow-x: auto;
        border-radius: 12px;
    }

    .table th, .table td {
        white-space: nowrap;
        vertical-align: middle;
    }

    /* Shadow and Border Effects */
    .shadow-pro {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        border: 1px solid #e3e6f0;
    }

    /* Alert Styling */
    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .alert-info {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
    }

    .alert-danger {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
    }

    /* Animation for loading states */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .data-row {
        animation: fadeIn 0.5s ease-out;
    }

    /* Print Styles */
    @media print {
        .report-header {
            background: #2c3e50 !important;
            -webkit-print-color-adjust: exact;
        }

        .table-header {
            background: #2c3e50 !important;
            -webkit-print-color-adjust: exact;
        }

        .shadow-pro {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>

<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Choose Project",
        allowClear: true
    });
});
</script>
