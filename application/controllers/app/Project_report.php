<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Project_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('projects_m');
        $this->load->model('users_m');
        $this->load->model('task_assign_m');
        $this->load->model('tasks_m');
        ini_set('pcre.backtrack_limit', 100000000);
        ini_set('memory_limit', '2048M');
    }

    public function project_employee() {
        if (!has_permission('project_report/project_employee')){
            redirect('app/dashboard/index');
        }

        $project_id = $this->input->get('project_id') ?? 0;
        $start_date = $this->input->get('start_date') ?? '';
        $end_date = $this->input->get('end_date') ?? '';

        $this->data['report_data'] = [];
        $this->data['total_hours'] = '0 hrs 0 min';
        $this->data['column_totals'] = [
            'total_date_range' => '0 hrs 0 min',
            'last_7_days' => '0 hrs 0 min',
            'last_15_days' => '0 hrs 0 min',
            'last_30_days' => '0 hrs 0 min',
            'last_60_days' => '0 hrs 0 min',
            'last_90_days' => '0 hrs 0 min',
            'last_180_days' => '0 hrs 0 min',
            'total_worked' => '0 hrs 0 min'
        ];

        // Generate report if project is selected
        if (!empty($project_id)) {
            // Set default dates if not provided
            if (empty($start_date) || empty($end_date)) {
                // Default to last 30 days if no dates provided
                $end_date = date('Y-m-d');
                $start_date = date('Y-m-d', strtotime('-30 days'));
            }

            // Validate date format if dates are provided
            $date_valid = true;
            $date_error = '';

            if (!DateTime::createFromFormat('Y-m-d', $start_date)) {
                $date_valid = false;
                $date_error = 'Invalid start date format. Please use YYYY-MM-DD format.';
            } elseif (!DateTime::createFromFormat('Y-m-d', $end_date)) {
                $date_valid = false;
                $date_error = 'Invalid end date format. Please use YYYY-MM-DD format.';
            } elseif (strtotime($start_date) > strtotime($end_date)) {
                $date_valid = false;
                $date_error = 'Start date cannot be after end date.';
            }

            if ($date_valid) {
                $this->data['report_data'] = $this->get_project_employee_report($project_id, $start_date, $end_date);
                $this->data['total_hours'] = $this->calculate_total_hours($this->data['report_data']);
                $this->data['actual_start_date'] = $start_date;
                $this->data['actual_end_date'] = $end_date;

                // Calculate column totals for the footer
                $this->data['column_totals'] = [
                    'total_date_range' => $this->calculate_column_total($this->data['report_data'], 'total_date_range'),
                    'last_7_days' => $this->calculate_column_total($this->data['report_data'], 'last_7_days'),
                    'last_15_days' => $this->calculate_column_total($this->data['report_data'], 'last_15_days'),
                    'last_30_days' => $this->calculate_column_total($this->data['report_data'], 'last_30_days'),
                    'last_60_days' => $this->calculate_column_total($this->data['report_data'], 'last_60_days'),
                    'last_90_days' => $this->calculate_column_total($this->data['report_data'], 'last_90_days'),
                    'last_180_days' => $this->calculate_column_total($this->data['report_data'], 'last_180_days'),
                    'total_worked' => $this->calculate_column_total($this->data['report_data'], 'total_worked')
                ];
            } else {
                $this->data['error_message'] = $date_error;
            }
        }

        // Get projects for dropdown
        $this->data['projects'] = array_column(
            $this->projects_m->get(null, ['id', 'title'], ['key' => 'title', 'direction' => 'ASC'])->result_array(), 
            'title', 
            'id'
        );

        $this->data['page_title'] = 'Project Employee Report';
        $this->data['page_name'] = 'project_report/project_employee';
        $this->load->view('app/index', $this->data);
    }

    private function get_project_employee_report($project_id, $start_date, $end_date) {
        // Get all employees who have ever worked on this project (regardless of date range)
        // Include both active (employee_status = 1) and inactive (employee_status = 0) employees if they have work records
        $this->db->distinct();
        $this->db->select('ta.user_id, u.name as employee_name, u.employee_status');
        $this->db->from('task_assign ta');
        $this->db->join('tasks t', 't.id = ta.task_id');
        $this->db->join('users u', 'u.id = ta.user_id');
        $this->db->where('t.project_id', $project_id);
        $this->db->where('ta.time_taken IS NOT NULL');
        $this->db->where('ta.time_taken !=', '00:00:00');
        $this->db->order_by('u.employee_status', 'DESC'); // Active employees first
        $this->db->order_by('u.name', 'ASC');

        $employees = $this->db->get()->result_array();

        $report_data = [];

        foreach ($employees as $employee) {
            $user_id = $employee['user_id'];
            $employee_name = $employee['employee_name'];
            $employee_status = $employee['employee_status'];

            // Calculate hours for different time periods relative to the end_date
            $total_date_range = $this->get_employee_project_hours($user_id, $project_id, $start_date, $end_date);

            // Calculate time periods from end_date backwards
            $end_date_obj = new DateTime($end_date);

            $last_7_start = clone $end_date_obj;
            $last_7_start->modify('-6 days'); // 7 days including end_date
            $last_7_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_7_start->format('Y-m-d'), $end_date);

            $last_15_start = clone $end_date_obj;
            $last_15_start->modify('-14 days'); // 15 days including end_date
            $last_15_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_15_start->format('Y-m-d'), $end_date);

            $last_30_start = clone $end_date_obj;
            $last_30_start->modify('-29 days'); // 30 days including end_date
            $last_30_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_30_start->format('Y-m-d'), $end_date);

            $last_60_start = clone $end_date_obj;
            $last_60_start->modify('-59 days'); // 60 days including end_date
            $last_60_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_60_start->format('Y-m-d'), $end_date);

            $last_90_start = clone $end_date_obj;
            $last_90_start->modify('-89 days'); // 90 days including end_date
            $last_90_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_90_start->format('Y-m-d'), $end_date);

            $last_180_start = clone $end_date_obj;
            $last_180_start->modify('-179 days'); // 180 days including end_date
            $last_180_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_180_start->format('Y-m-d'), $end_date);

            // Calculate total worked (all time for this employee on this project)
            $total_worked = $this->get_employee_project_hours($user_id, $project_id, '1900-01-01', date('Y-m-d'));

            $report_data[] = [
                'user_id' => $user_id,
                'employee_name' => $employee_name,
                'employee_status' => $employee_status,
                'total_date_range' => $total_date_range,
                'last_7_days' => $last_7_days,
                'last_15_days' => $last_15_days,
                'last_30_days' => $last_30_days,
                'last_60_days' => $last_60_days,
                'last_90_days' => $last_90_days,
                'last_180_days' => $last_180_days,
                'total_worked' => $total_worked
            ];
        }

        return $report_data;
    }

    private function get_employee_project_hours($user_id, $project_id, $start_date, $end_date) {
        $this->db->select('ta.time_taken');
        $this->db->from('task_assign ta');
        $this->db->join('tasks t', 't.id = ta.task_id');
        $this->db->where('ta.user_id', $user_id);
        $this->db->where('t.project_id', $project_id);
        $this->db->where('ta.job_date >=', $start_date);
        $this->db->where('ta.job_date <=', $end_date);
        $this->db->where('ta.time_taken IS NOT NULL');
        $this->db->where('ta.time_taken !=', '00:00:00');
        $this->db->where('ta.time_taken <', '06:00:00'); // Filter out invalid entries

        $time_entries = $this->db->get()->result_array();
        $time_taken_array = array_column($time_entries, 'time_taken');

        // Filter out any null or empty values
        $time_taken_array = array_filter($time_taken_array, function($time) {
            return !empty($time) && $time !== '00:00:00';
        });

        if (empty($time_taken_array)) {
            return '0 hrs 0 min';
        }

        $total_seconds = add_duration($time_taken_array, 'seconds');
        return $this->format_readable_time($total_seconds);
    }

    private function calculate_total_hours($report_data) {
        if (empty($report_data)) {
            return '0 hrs 0 min';
        }

        $total_seconds = 0;

        foreach ($report_data as $employee_data) {
            if (isset($employee_data['total_date_range'])) {
                $total_seconds += $this->readable_time_to_seconds($employee_data['total_date_range']);
            }
        }

        return $this->format_readable_time($total_seconds);
    }

    private function calculate_column_total($report_data, $column) {
        if (empty($report_data)) {
            return '0 hrs 0 min';
        }

        $total_seconds = 0;

        foreach ($report_data as $employee_data) {
            if (isset($employee_data[$column])) {
                $total_seconds += $this->readable_time_to_seconds($employee_data[$column]);
            }
        }

        return $this->format_readable_time($total_seconds);
    }

    private function format_readable_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return $hours . ' hrs ' . $minutes . ' min';
    }

    private function readable_time_to_seconds($readable_time) {
        // Convert "45 hrs 30 min" back to seconds for calculations
        if (preg_match('/(\d+)\s*hrs?\s*(\d+)\s*min/', $readable_time, $matches)) {
            $hours = (int)$matches[1];
            $minutes = (int)$matches[2];
            return ($hours * 3600) + ($minutes * 60);
        }
        return 0;
    }


}
